"use client";

import { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Upload, File, X, CheckCircle, Clock, AlertCircle } from "lucide-react";

interface UploadedFile {
  id: string;
  name: string;
  size: number;
  type: string;
  status?: 'uploading' | 'processing' | 'indexed' | 'error';
  errorMessage?: string;
}

export function DocumentUpload() {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Fetch document status on component mount and periodically
  useEffect(() => {
    fetchDocumentStatus();
    const interval = setInterval(fetchDocumentStatus, 5000); // Check every 5 seconds
    return () => clearInterval(interval);
  }, []);

  const fetchDocumentStatus = async () => {
    try {
      const response = await fetch('/api/documents/stats');
      if (response.ok) {
        const data = await response.json();
        setUploadedFiles(data.documents.map((doc: any) => ({
          id: doc.id,
          name: doc.originalName || doc.filename,
          size: doc.size,
          type: '', // We don't store type in the backend
          status: doc.status,
          errorMessage: doc.errorMessage,
        })));
      }
    } catch (error) {
      console.error('Failed to fetch document status:', error);
    }
  };

  const handleUpload = async (files: File[]) => {
    try {
      for (const file of files) {
        // Create FormData for upload
        const formData = new FormData();
        formData.append('file', file);

        // Upload file to API
        const response = await fetch('/api/documents/upload', {
          method: 'POST',
          body: formData,
        });

        if (!response.ok) {
          throw new Error(`Failed to upload ${file.name}`);
        }

        const result = await response.json();
        console.log(`Successfully uploaded: ${file.name}`);
      }

      // Refresh document status after upload
      await fetchDocumentStatus();
    } catch (error) {
      console.error('Upload error:', error);
      throw error; // Let the enhanced file upload component handle the error
    }
  };

  const handleRemoveFile = async (fileId: string) => {
    try {
      const response = await fetch(`/api/documents/${fileId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setUploadedFiles(prev => prev.filter(file => file.id !== fileId));
      } else {
        alert('Failed to remove file');
      }
    } catch (error) {
      console.error('Remove file error:', error);
      alert('Failed to remove file');
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getStatusIcon = (status?: string) => {
    switch (status) {
      case 'uploading':
      case 'processing':
        return <Clock className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'indexed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <CheckCircle className="h-4 w-4 text-green-500" />;
    }
  };

  const getStatusText = (status?: string) => {
    switch (status) {
      case 'uploading':
        return 'Uploading...';
      case 'processing':
        return 'Processing...';
      case 'indexed':
        return 'Ready';
      case 'error':
        return 'Error';
      default:
        return 'Ready';
    }
  };



  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    setIsUploading(true);
    try {
      await handleUpload(Array.from(files));
    } catch (error) {
      console.error('Upload failed:', error);
    } finally {
      setIsUploading(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      setIsUploading(true);
      try {
        await handleUpload(Array.from(files));
      } catch (error) {
        console.error('Upload failed:', error);
      } finally {
        setIsUploading(false);
      }
    }
  };

  return (
    <div className="space-y-4">
      <Card
        className={`border-2 border-dashed transition-all duration-300 cursor-pointer group ${
          isDragOver
            ? 'border-primary bg-primary/10 scale-[1.02] shadow-lg'
            : 'border-border hover:border-primary/50 hover:bg-muted/30 hover:shadow-md'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={() => fileInputRef.current?.click()}
      >
        <CardContent className="p-8 text-center">
          <div className={`mx-auto w-16 h-16 rounded-full flex items-center justify-center mb-4 transition-all duration-300 ${
            isDragOver
              ? 'bg-primary text-primary-foreground scale-110'
              : 'bg-muted text-muted-foreground group-hover:scale-105 group-hover:bg-primary/20'
          }`}>
            <Upload className={`w-8 h-8 transition-transform duration-300 ${isDragOver ? 'scale-110' : 'group-hover:scale-110'}`} />
          </div>

          <h3 className={`text-lg font-semibold mb-2 transition-colors duration-300 ${
            isDragOver ? 'text-primary' : 'text-foreground'
          }`}>
            {isDragOver ? '✨ Drop files here' : 'Upload your documents'}
          </h3>

          <p className="text-muted-foreground mb-4">
            Drag and drop files here, or click to browse
          </p>

          <div className="flex flex-wrap justify-center gap-2 text-sm text-muted-foreground mb-4">
            <span className="px-2 py-1 bg-muted/50 rounded-full">Max file size: 10 MB</span>
            <span className="px-2 py-1 bg-muted/50 rounded-full">Max files: 10</span>
          </div>

          <Button
            variant="outline"
            disabled={isUploading}
            className="transition-all duration-300 hover:scale-105 hover:shadow-md"
          >
            {isUploading ? (
              <>
                <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2" />
                Uploading...
              </>
            ) : (
              'Choose Files'
            )}
          </Button>

          <p className="text-xs text-muted-foreground mt-2">
            Supported formats: .txt, .md
          </p>
        </CardContent>

        <input
          ref={fileInputRef}
          type="file"
          multiple
          className="hidden"
          accept=".txt,.md"
          onChange={handleFileSelect}
        />
      </Card>

      {uploadedFiles.length > 0 && (
        <div className="space-y-3">
          <h3 className="text-sm font-medium flex items-center gap-2">
            <div className="w-5 h-5 rounded bg-primary/10 flex items-center justify-center">
              📁
            </div>
            Uploaded Documents ({uploadedFiles.length})
          </h3>
          {uploadedFiles.map((file) => (
            <Card key={file.id} className="p-4 transition-all duration-200 hover:shadow-md hover:scale-[1.01] group">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 rounded-lg bg-muted/50 flex items-center justify-center">
                    <File className="h-4 w-4 text-muted-foreground" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium group-hover:text-primary transition-colors">{file.name}</p>
                    <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                      <span className="px-2 py-0.5 bg-muted/50 rounded-full">{formatFileSize(file.size)}</span>
                      <div className="flex items-center space-x-1">
                        {getStatusIcon(file.status)}
                        <span className={`font-medium ${
                          file.status === 'indexed' ? 'text-green-600' :
                          file.status === 'error' ? 'text-red-600' :
                          'text-blue-600'
                        }`}>
                          {getStatusText(file.status)}
                        </span>
                      </div>
                    </div>
                    {file.errorMessage && (
                      <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded-md">
                        <p className="text-xs text-red-600">{file.errorMessage}</p>
                      </div>
                    )}
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleRemoveFile(file.id)}
                  disabled={file.status === 'processing'}
                  className="opacity-0 group-hover:opacity-100 transition-opacity hover:bg-red-50 hover:text-red-600"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </Card>
          ))}
        </div>
      )}

      {uploadedFiles.length === 0 && (
        <div className="text-center py-8 text-muted-foreground">
          <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-muted/30 flex items-center justify-center">
            <File className="w-8 h-8 opacity-50" />
          </div>
          <p className="text-sm">No files uploaded yet</p>
          <p className="text-xs mt-1 opacity-75">Upload documents to start chatting</p>
        </div>
      )}
    </div>
  );
}
