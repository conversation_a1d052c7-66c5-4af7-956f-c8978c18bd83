@tailwind base;
@tailwind components;
@tailwind utilities;

/* Highlight.js theme for code syntax highlighting */
@import 'highlight.js/styles/github.css';

@layer base {
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer utilities {
  /* Custom scrollbar styles */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: hsl(215.4 16.3% 46.9%) transparent;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: hsl(215.4 16.3% 46.9%);
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: hsl(222.2 84% 4.9%);
  }

  /* Animation utilities */
  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-bounce-slow {
    animation: bounce 2s infinite;
  }

  /* Smooth transitions for interactive elements */
  .transition-smooth {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Focus styles */
  .focus-visible-ring {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2;
  }

  /* Hover effects */
  .hover-lift {
    @apply transition-transform duration-200 hover:scale-[1.02];
  }

  .hover-glow {
    @apply transition-shadow duration-200 hover:shadow-lg;
  }
}
