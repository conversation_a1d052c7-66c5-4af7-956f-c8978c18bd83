[{"D:\\code\\chatdoc-v1\\src\\app\\api\\chat\\route.ts": "1", "D:\\code\\chatdoc-v1\\src\\app\\api\\documents\\enhanced-stats\\route.ts": "2", "D:\\code\\chatdoc-v1\\src\\app\\api\\documents\\ingest-url\\route.ts": "3", "D:\\code\\chatdoc-v1\\src\\app\\api\\documents\\stats\\route.ts": "4", "D:\\code\\chatdoc-v1\\src\\app\\api\\documents\\upload\\route.ts": "5", "D:\\code\\chatdoc-v1\\src\\app\\api\\documents\\[id]\\route.ts": "6", "D:\\code\\chatdoc-v1\\src\\app\\layout.tsx": "7", "D:\\code\\chatdoc-v1\\src\\app\\page.tsx": "8", "D:\\code\\chatdoc-v1\\src\\components\\chat\\chat-interface.tsx": "9", "D:\\code\\chatdoc-v1\\src\\components\\documents\\document-upload.tsx": "10", "D:\\code\\chatdoc-v1\\src\\components\\documents\\url-ingestion.tsx": "11", "D:\\code\\chatdoc-v1\\src\\components\\providers\\query-provider.tsx": "12", "D:\\code\\chatdoc-v1\\src\\components\\ui\\alert.tsx": "13", "D:\\code\\chatdoc-v1\\src\\components\\ui\\animated-chat-interface.tsx": "14", "D:\\code\\chatdoc-v1\\src\\components\\ui\\animated-form.tsx": "15", "D:\\code\\chatdoc-v1\\src\\components\\ui\\badge.tsx": "16", "D:\\code\\chatdoc-v1\\src\\components\\ui\\button.tsx": "17", "D:\\code\\chatdoc-v1\\src\\components\\ui\\card.tsx": "18", "D:\\code\\chatdoc-v1\\src\\components\\ui\\collapsible.tsx": "19", "D:\\code\\chatdoc-v1\\src\\components\\ui\\dialog.tsx": "20", "D:\\code\\chatdoc-v1\\src\\components\\ui\\enhanced-file-upload.tsx": "21", "D:\\code\\chatdoc-v1\\src\\components\\ui\\input.tsx": "22", "D:\\code\\chatdoc-v1\\src\\components\\ui\\label.tsx": "23", "D:\\code\\chatdoc-v1\\src\\components\\ui\\markdown-renderer.tsx": "24", "D:\\code\\chatdoc-v1\\src\\components\\ui\\progress.tsx": "25", "D:\\code\\chatdoc-v1\\src\\components\\ui\\scroll-area.tsx": "26", "D:\\code\\chatdoc-v1\\src\\components\\ui\\skeleton.tsx": "27", "D:\\code\\chatdoc-v1\\src\\components\\ui\\textarea.tsx": "28", "D:\\code\\chatdoc-v1\\src\\lib\\deepseek-llm.ts": "29", "D:\\code\\chatdoc-v1\\src\\lib\\document-manager.ts": "30", "D:\\code\\chatdoc-v1\\src\\lib\\doubao-embedding.ts": "31", "D:\\code\\chatdoc-v1\\src\\lib\\enhanced-document-service.ts": "32", "D:\\code\\chatdoc-v1\\src\\lib\\llamaindex-service.ts": "33", "D:\\code\\chatdoc-v1\\src\\lib\\url-document-service.ts": "34", "D:\\code\\chatdoc-v1\\src\\lib\\utils.ts": "35"}, {"size": 3587, "mtime": 1751905196428, "results": "36", "hashOfConfig": "37"}, {"size": 1670, "mtime": 1751905315000, "results": "38", "hashOfConfig": "37"}, {"size": 1709, "mtime": 1751909489461, "results": "39", "hashOfConfig": "37"}, {"size": 809, "mtime": 1751902803220, "results": "40", "hashOfConfig": "37"}, {"size": 2437, "mtime": 1751905264918, "results": "41", "hashOfConfig": "37"}, {"size": 1661, "mtime": 1751905297061, "results": "42", "hashOfConfig": "37"}, {"size": 641, "mtime": 1751901878004, "results": "43", "hashOfConfig": "37"}, {"size": 2587, "mtime": 1751914514545, "results": "44", "hashOfConfig": "37"}, {"size": 2181, "mtime": 1751912553458, "results": "45", "hashOfConfig": "37"}, {"size": 10578, "mtime": 1751915150995, "results": "46", "hashOfConfig": "37"}, {"size": 11680, "mtime": 1751915324740, "results": "47", "hashOfConfig": "37"}, {"size": 551, "mtime": 1751901889870, "results": "48", "hashOfConfig": "37"}, {"size": 1584, "mtime": 1751909560766, "results": "49", "hashOfConfig": "37"}, {"size": 12545, "mtime": 1751915259402, "results": "50", "hashOfConfig": "37"}, {"size": 6541, "mtime": 1751912430046, "results": "51", "hashOfConfig": "37"}, {"size": 1631, "mtime": 1751912245642, "results": "52", "hashOfConfig": "37"}, {"size": 1835, "mtime": 1751901821784, "results": "53", "hashOfConfig": "37"}, {"size": 1877, "mtime": 1751901842173, "results": "54", "hashOfConfig": "37"}, {"size": 346, "mtime": 1751909550358, "results": "55", "hashOfConfig": "37"}, {"size": 3835, "mtime": 1751913563525, "results": "56", "hashOfConfig": "37"}, {"size": 14086, "mtime": 1751913667810, "results": "57", "hashOfConfig": "37"}, {"size": 824, "mtime": 1751901830387, "results": "58", "hashOfConfig": "37"}, {"size": 710, "mtime": 1751909537258, "results": "59", "hashOfConfig": "37"}, {"size": 5512, "mtime": 1751909664303, "results": "60", "hashOfConfig": "37"}, {"size": 740, "mtime": 1751912245650, "results": "61", "hashOfConfig": "37"}, {"size": 1645, "mtime": 1751912245669, "results": "62", "hashOfConfig": "37"}, {"size": 276, "mtime": 1751912245681, "results": "63", "hashOfConfig": "37"}, {"size": 772, "mtime": 1751909544162, "results": "64", "hashOfConfig": "37"}, {"size": 2431, "mtime": 1751904352064, "results": "65", "hashOfConfig": "37"}, {"size": 3086, "mtime": 1751902591123, "results": "66", "hashOfConfig": "37"}, {"size": 3799, "mtime": 1751905091570, "results": "67", "hashOfConfig": "37"}, {"size": 15485, "mtime": 1751908364632, "results": "68", "hashOfConfig": "37"}, {"size": 4502, "mtime": 1751904628841, "results": "69", "hashOfConfig": "37"}, {"size": 6972, "mtime": 1751909477260, "results": "70", "hashOfConfig": "37"}, {"size": 166, "mtime": 1751912238626, "results": "71", "hashOfConfig": "37"}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1slxjmv", {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\code\\chatdoc-v1\\src\\app\\api\\chat\\route.ts", [], [], "D:\\code\\chatdoc-v1\\src\\app\\api\\documents\\enhanced-stats\\route.ts", [], [], "D:\\code\\chatdoc-v1\\src\\app\\api\\documents\\ingest-url\\route.ts", [], [], "D:\\code\\chatdoc-v1\\src\\app\\api\\documents\\stats\\route.ts", [], [], "D:\\code\\chatdoc-v1\\src\\app\\api\\documents\\upload\\route.ts", [], [], "D:\\code\\chatdoc-v1\\src\\app\\api\\documents\\[id]\\route.ts", [], [], "D:\\code\\chatdoc-v1\\src\\app\\layout.tsx", [], [], "D:\\code\\chatdoc-v1\\src\\app\\page.tsx", [], [], "D:\\code\\chatdoc-v1\\src\\components\\chat\\chat-interface.tsx", [], [], "D:\\code\\chatdoc-v1\\src\\components\\documents\\document-upload.tsx", [], [], "D:\\code\\chatdoc-v1\\src\\components\\documents\\url-ingestion.tsx", [], [], "D:\\code\\chatdoc-v1\\src\\components\\providers\\query-provider.tsx", [], [], "D:\\code\\chatdoc-v1\\src\\components\\ui\\alert.tsx", [], [], "D:\\code\\chatdoc-v1\\src\\components\\ui\\animated-chat-interface.tsx", [], [], "D:\\code\\chatdoc-v1\\src\\components\\ui\\animated-form.tsx", ["177", "178", "179", "180", "181", "182"], [], "D:\\code\\chatdoc-v1\\src\\components\\ui\\badge.tsx", [], [], "D:\\code\\chatdoc-v1\\src\\components\\ui\\button.tsx", [], [], "D:\\code\\chatdoc-v1\\src\\components\\ui\\card.tsx", [], [], "D:\\code\\chatdoc-v1\\src\\components\\ui\\collapsible.tsx", ["183"], [], "D:\\code\\chatdoc-v1\\src\\components\\ui\\dialog.tsx", [], [], "D:\\code\\chatdoc-v1\\src\\components\\ui\\enhanced-file-upload.tsx", ["184", "185", "186", "187"], [], "D:\\code\\chatdoc-v1\\src\\components\\ui\\input.tsx", ["188"], [], "D:\\code\\chatdoc-v1\\src\\components\\ui\\label.tsx", [], [], "D:\\code\\chatdoc-v1\\src\\components\\ui\\markdown-renderer.tsx", ["189"], [], "D:\\code\\chatdoc-v1\\src\\components\\ui\\progress.tsx", [], [], "D:\\code\\chatdoc-v1\\src\\components\\ui\\scroll-area.tsx", [], [], "D:\\code\\chatdoc-v1\\src\\components\\ui\\skeleton.tsx", [], [], "D:\\code\\chatdoc-v1\\src\\components\\ui\\textarea.tsx", ["190"], [], "D:\\code\\chatdoc-v1\\src\\lib\\deepseek-llm.ts", ["191"], [], "D:\\code\\chatdoc-v1\\src\\lib\\document-manager.ts", ["192"], [], "D:\\code\\chatdoc-v1\\src\\lib\\doubao-embedding.ts", ["193", "194"], [], "D:\\code\\chatdoc-v1\\src\\lib\\enhanced-document-service.ts", ["195", "196", "197"], [], "D:\\code\\chatdoc-v1\\src\\lib\\llamaindex-service.ts", ["198"], [], "D:\\code\\chatdoc-v1\\src\\lib\\url-document-service.ts", ["199", "200"], [], "D:\\code\\chatdoc-v1\\src\\lib\\utils.ts", [], [], {"ruleId": "201", "severity": 2, "message": "202", "line": 4, "column": 28, "nodeType": null, "messageId": "203", "endLine": 4, "endColumn": 37}, {"ruleId": "201", "severity": 2, "message": "204", "line": 7, "column": 3, "nodeType": null, "messageId": "203", "endLine": 7, "endColumn": 8}, {"ruleId": "201", "severity": 2, "message": "205", "line": 8, "column": 3, "nodeType": null, "messageId": "203", "endLine": 8, "endColumn": 7}, {"ruleId": "201", "severity": 2, "message": "206", "line": 9, "column": 3, "nodeType": null, "messageId": "203", "endLine": 9, "endColumn": 7}, {"ruleId": "201", "severity": 2, "message": "207", "line": 10, "column": 3, "nodeType": null, "messageId": "203", "endLine": 10, "endColumn": 6}, {"ruleId": "201", "severity": 2, "message": "208", "line": 16, "column": 3, "nodeType": null, "messageId": "203", "endLine": 16, "endColumn": 7}, {"ruleId": "201", "severity": 2, "message": "209", "line": 1, "column": 13, "nodeType": null, "messageId": "203", "endLine": 1, "endColumn": 18}, {"ruleId": "201", "severity": 2, "message": "210", "line": 2, "column": 81, "nodeType": null, "messageId": "203", "endLine": 2, "endColumn": 84}, {"ruleId": "211", "severity": 1, "message": "212", "line": 121, "column": 6, "nodeType": "213", "endLine": 121, "endColumn": 40, "suggestions": "214"}, {"ruleId": "201", "severity": 2, "message": "215", "line": 145, "column": 16, "nodeType": null, "messageId": "203", "endLine": 145, "endColumn": 21}, {"ruleId": "216", "severity": 1, "message": "217", "line": 336, "column": 25, "nodeType": "218", "endLine": 340, "endColumn": 27}, {"ruleId": "219", "severity": 2, "message": "220", "line": 5, "column": 18, "nodeType": "221", "messageId": "222", "endLine": 5, "endColumn": 28, "suggestions": "223"}, {"ruleId": "216", "severity": 1, "message": "217", "line": 171, "column": 13, "nodeType": "218", "endLine": 175, "endColumn": 15}, {"ruleId": "219", "severity": 2, "message": "220", "line": 5, "column": 18, "nodeType": "221", "messageId": "222", "endLine": 5, "endColumn": 31, "suggestions": "224"}, {"ruleId": "225", "severity": 2, "message": "226", "line": 19, "column": 9, "nodeType": "227", "messageId": "228", "endLine": 19, "endColumn": 12, "suggestions": "229"}, {"ruleId": "201", "severity": 2, "message": "230", "line": 1, "column": 31, "nodeType": null, "messageId": "203", "endLine": 1, "endColumn": 36}, {"ruleId": "225", "severity": 2, "message": "226", "line": 119, "column": 64, "nodeType": "227", "messageId": "228", "endLine": 119, "endColumn": 67, "suggestions": "231"}, {"ruleId": "225", "severity": 2, "message": "226", "line": 121, "column": 52, "nodeType": "227", "messageId": "228", "endLine": 121, "endColumn": 55, "suggestions": "232"}, {"ruleId": "201", "severity": 2, "message": "215", "line": 116, "column": 16, "nodeType": null, "messageId": "203", "endLine": 116, "endColumn": 21}, {"ruleId": "201", "severity": 2, "message": "233", "line": 332, "column": 32, "nodeType": null, "messageId": "203", "endLine": 332, "endColumn": 44}, {"ruleId": "225", "severity": 2, "message": "226", "line": 355, "column": 63, "nodeType": "227", "messageId": "228", "endLine": 355, "endColumn": 66, "suggestions": "234"}, {"ruleId": "201", "severity": 2, "message": "233", "line": 95, "column": 32, "nodeType": null, "messageId": "203", "endLine": 95, "endColumn": 44}, {"ruleId": "235", "severity": 2, "message": "236", "line": 150, "column": 11, "nodeType": "221", "messageId": "237", "endLine": 150, "endColumn": 26, "fix": "238"}, {"ruleId": "201", "severity": 2, "message": "215", "line": 227, "column": 14, "nodeType": null, "messageId": "203", "endLine": 227, "endColumn": 19}, "@typescript-eslint/no-unused-vars", "'useEffect' is defined but never used.", "unusedVar", "'Globe' is defined but never used.", "'Lock' is defined but never used.", "'User' is defined but never used.", "'Key' is defined but never used.", "'Send' is defined but never used.", "'React' is defined but never used.", "'Eye' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useCallback has missing dependencies: 'uploadFiles' and 'validateFile'. Either include them or remove the dependency array.", "ArrayExpression", ["239"], "'error' is defined but never used.", "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "@typescript-eslint/no-empty-object-type", "An interface declaring no members is equivalent to its supertype.", "Identifier", "noEmptyInterfaceWithSuper", ["240"], ["241"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["242", "243"], "'mkdir' is defined but never used.", ["244", "245"], ["246", "247"], "'_chatHistory' is assigned a value but never used.", ["248", "249"], "prefer-const", "'markdownContent' is never reassigned. Use 'const' instead.", "useConst", {"range": "250", "text": "251"}, {"desc": "252", "fix": "253"}, {"messageId": "254", "fix": "255", "desc": "256"}, {"messageId": "254", "fix": "257", "desc": "256"}, {"messageId": "258", "fix": "259", "desc": "260"}, {"messageId": "261", "fix": "262", "desc": "263"}, {"messageId": "258", "fix": "264", "desc": "260"}, {"messageId": "261", "fix": "265", "desc": "263"}, {"messageId": "258", "fix": "266", "desc": "260"}, {"messageId": "261", "fix": "267", "desc": "263"}, {"messageId": "258", "fix": "268", "desc": "260"}, {"messageId": "261", "fix": "269", "desc": "263"}, [4482, 4547], "const markdownContent = this.turndownService.turndown(htmlContent);", "Update the dependencies array to be: [files.length, maxFiles, onUpload, uploadFiles, validateFile]", {"range": "270", "text": "271"}, "replaceEmptyInterfaceWithSuper", {"range": "272", "text": "273"}, "Replace empty interface with a type alias.", {"range": "274", "text": "275"}, "suggestUnknown", {"range": "276", "text": "277"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "278", "text": "279"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "280", "text": "277"}, {"range": "281", "text": "279"}, {"range": "282", "text": "277"}, {"range": "283", "text": "279"}, {"range": "284", "text": "277"}, {"range": "285", "text": "279"}, [3809, 3843], "[files.length, maxFiles, onUpload, uploadFiles, validateFile]", [73, 150], "type InputProps = React.InputHTMLAttributes<HTMLInputElement>", [73, 159], "type TextareaProps = React.TextareaHTMLAttributes<HTMLTextAreaElement>", [282, 285], "unknown", [282, 285], "never", [3381, 3384], [3381, 3384], [3461, 3464], [3461, 3464], [12539, 12542], [12539, 12542]]