"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AnimatedInput, CollapsibleSection, type ValidationState } from "@/components/ui/animated-form";
import { Globe, Lock, User, Key, AlertCircle, CheckCircle, Send } from "lucide-react";
import { motion } from "framer-motion";

interface UrlIngestionProps {
  onSuccess?: (result: { documentId: string; filename: string; contentPreview: string }) => void;
}

export function UrlIngestion({ onSuccess }: UrlIngestionProps) {
  const [formData, setFormData] = useState({
    url: "",
    authType: "none" as "none" | "basic",
    username: "",
    password: "",
    customHeaders: ""
  });
  const [isLoading, setIsLoading] = useState(false);
  const [openSections, setOpenSections] = useState({
    url: true,
    auth: false,
    headers: false
  });
  const [validations, setValidations] = useState<Record<string, ValidationState>>({});
  const [result, setResult] = useState<{
    type: 'success' | 'error';
    message: string;
    details?: string;
  } | null>(null);

  // Validation functions
  const validateUrl = (url: string): ValidationState => {
    if (!url) return { isValid: false, message: "URL is required" };
    try {
      new URL(url);
      return { isValid: true, message: "" };
    } catch {
      return { isValid: false, message: "Please enter a valid URL" };
    }
  };

  const validateAuth = (): ValidationState => {
    if (formData.authType === "none") return { isValid: true, message: "" };

    if (formData.authType === "basic") {
      if (!formData.username || !formData.password) {
        return { isValid: false, message: "Username and password are required" };
      }
    }

    return { isValid: true, message: "" };
  };

  // Update validations when form data changes
  React.useEffect(() => {
    setValidations({
      url: validateUrl(formData.url),
      auth: validateAuth()
    });
  }, [formData]);

  const toggleSection = (section: keyof typeof openSections) => {
    setOpenSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const updateFormData = (field: keyof typeof formData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.url.trim() || isLoading) return;

    setIsLoading(true);
    setResult(null);

    try {
      // Parse custom headers if provided
      let parsedHeaders: Record<string, string> = {};
      if (formData.customHeaders.trim()) {
        try {
          const lines = formData.customHeaders.split('\n').filter(line => line.trim());
          for (const line of lines) {
            const [key, ...valueParts] = line.split(':');
            if (key && valueParts.length > 0) {
              parsedHeaders[key.trim()] = valueParts.join(':').trim();
            }
          }
        } catch (headerError) {
          setResult({
            type: 'error',
            message: 'Invalid custom headers format',
            details: 'Please use format: "Header-Name: Header-Value" (one per line)'
          });
          setIsLoading(false);
          return;
        }
      }

      const response = await fetch('/api/documents/ingest-url', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          url: formData.url.trim(),
          username: formData.username.trim() || undefined,
          password: formData.password.trim() || undefined,
          customHeaders: Object.keys(parsedHeaders).length > 0 ? parsedHeaders : undefined,
        }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        setResult({
          type: 'success',
          message: 'Document successfully ingested from URL!',
          details: `Filename: ${data.filename}`
        });

        // Clear form
        setFormData({
          url: "",
          authType: "none",
          username: "",
          password: "",
          customHeaders: ""
        });
        setOpenSections({ url: true, auth: false, headers: false });

        // Call success callback
        if (onSuccess) {
          onSuccess({
            documentId: data.documentId,
            filename: data.filename,
            contentPreview: data.contentPreview
          });
        }
      } else {
        setResult({
          type: 'error',
          message: 'Failed to ingest document from URL',
          details: data.error || 'Unknown error occurred'
        });
      }
    } catch (error) {
      console.error('URL ingestion error:', error);
      setResult({
        type: 'error',
        message: 'Network error occurred',
        details: 'Please check your internet connection and try again'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const isFormValid = validations.url?.isValid && validations.auth?.isValid;

  return (
    <Card className="shadow-sm">
      <CardHeader>
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center"
        >
          <CardTitle className="flex items-center justify-center space-x-2">
            <div className="w-8 h-8 rounded-lg bg-primary/10 flex items-center justify-center">
              🌐
            </div>
            <span>Ingest from URL</span>
          </CardTitle>
          <CardDescription>
            Fetch and process documents from web URLs
          </CardDescription>
        </motion.div>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* URL Section */}
          <CollapsibleSection
            title="URL Configuration"
            icon={<Globe size={20} />}
            isOpen={openSections.url}
            onToggle={() => toggleSection("url")}
            badge="Required"
          >
            <AnimatedInput
              label="Source URL"
              type="url"
              value={formData.url}
              onChange={(value) => updateFormData("url", value)}
              placeholder="https://example.com/article"
              validation={validations.url}
              icon={<Globe size={16} />}
              required
            />
          </CollapsibleSection>

          {/* Authentication Section */}
          <CollapsibleSection
            title="Authentication"
            icon={<Lock size={20} />}
            isOpen={openSections.auth}
            onToggle={() => toggleSection("auth")}
            badge={formData.authType !== "none" ? formData.authType.toUpperCase() : "None"}
          >
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">Authentication Type</label>
                <select
                  value={formData.authType}
                  onChange={(e) => updateFormData("authType", e.target.value)}
                  className="w-full h-12 px-3 border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
                >
                  <option value="none">No Authentication</option>
                  <option value="basic">Basic Auth</option>
                </select>
              </div>

              {formData.authType === "basic" && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: "auto" }}
                  exit={{ opacity: 0, height: 0 }}
                  className="space-y-4"
                >
                  <AnimatedInput
                    label="Username"
                    value={formData.username}
                    onChange={(value) => updateFormData("username", value)}
                    icon={<User size={16} />}
                    required
                  />
                  <AnimatedInput
                    label="Password"
                    type="password"
                    value={formData.password}
                    onChange={(value) => updateFormData("password", value)}
                    icon={<Key size={16} />}
                    required
                  />
                </motion.div>
              )}

              {validations.auth && !validations.auth.isValid && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="flex items-center gap-2 text-sm text-destructive"
                >
                  <AlertCircle size={14} />
                  {validations.auth.message}
                </motion.div>
              )}
            </div>
          </CollapsibleSection>

          {/* Submit Button */}
          <motion.button
            type="submit"
            disabled={!isFormValid || isLoading}
            className={`w-full h-12 rounded-lg font-medium transition-all duration-200 flex items-center justify-center gap-2 ${
              isFormValid && !isLoading
                ? "bg-primary text-primary-foreground hover:bg-primary/90 shadow-lg hover:shadow-xl"
                : "bg-muted text-muted-foreground cursor-not-allowed"
            }`}
            whileHover={isFormValid && !isLoading ? { scale: 1.02 } : {}}
            whileTap={isFormValid && !isLoading ? { scale: 0.98 } : {}}
          >
            {isLoading ? (
              <>
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  className="w-4 h-4 border-2 border-current border-t-transparent rounded-full"
                />
                Processing...
              </>
            ) : (
              <>
                <Send size={16} />
                Ingest URL
              </>
            )}
          </motion.button>

          {/* Result Display */}
          {result && (
            <Alert className={result.type === 'success' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
              <div className="flex items-start space-x-2">
                {result.type === 'success' ? (
                  <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                ) : (
                  <AlertCircle className="h-4 w-4 text-red-600 mt-0.5" />
                )}
                <div className="flex-1">
                  <AlertDescription className={result.type === 'success' ? 'text-green-800' : 'text-red-800'}>
                    <div className="font-medium">{result.message}</div>
                    {result.details && (
                      <div className="text-sm mt-1 opacity-90">{result.details}</div>
                    )}
                  </AlertDescription>
                </div>
              </div>
            </Alert>
          )}
        </form>

        {/* Usage Instructions */}
        <div className="mt-6 p-4 bg-muted rounded-lg">
          <h4 className="text-sm font-medium mb-2">Supported Content:</h4>
          <ul className="text-xs text-muted-foreground space-y-1">
            <li>• HTML web pages (articles, blogs, documentation)</li>
            <li>• HTTP and HTTPS protocols only</li>
            <li>• Basic authentication supported</li>
            <li>• Custom headers for API access</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}
